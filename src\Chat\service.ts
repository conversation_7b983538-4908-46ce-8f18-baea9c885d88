import { ClientAgentType } from "@/types/client";
import axios from "../service/axiosInstance";
import { ModelType } from "./type";

export function saveConversation(chatName: string, agentId: number) {
  return axios.post<any>(`/knowledge/kbChatBI/createNewConversations`, {
    chatName,
    agentId,
  });
}

export function updateConversationName(chatName: string, chatId: number = 0) {
  return axios.post<any>(
    `/chat/manage/updateChatName?chatName=${chatName}&chatId=${chatId}`
  );
}

export function deleteConversation(chatId: number) {
  return axios.post<any>(
    `/knowledge/kbChatBI/deleteConversations?chatId=${chatId}`
  );
}

export function getAllConversations(agentId?: number) {
  return axios.get<any>(`/knowledge/kbChatBI/conversations`, {
    params: { agentId: agentId },
  });
}

export function getModelList() {
  return axios.get<ModelType[]>(`/chat/conf/modelList/dataSet`);
}

export function updateQAFeedback(questionId: number, score: number) {
  return axios.post<any>(
    `/chat/manage/updateQAFeedback?id=${questionId}&score=${score}&feedback=`
  );
}

export function queryMetricSuggestion(modelId: number) {
  return axios.get<any>(`/chat/recommend/metric/${modelId}`);
}

export function querySuggestion(modelId: number) {
  return axios.get<any>(`/chat/recommend/${modelId}`);
}

export function queryRecommendQuestions() {
  return axios.get<any>(`/chat/recommend/question`);
}

export function queryAgentList() {
  return axios.get<ClientAgentType[]>(
    `/knowledge/kbAssistantConfig/getAllApps`
  );
}

<template>
  <div class="chat-footer">
    <div class="tools" v-if="!isDebugMode">
      <div class="tool-item" @click="onAddConversation">
        <IconFont type="icon-c003xiaoxiduihua" class="tool-icon" />
        <div>新对话</div>
      </div>
      <div v-if="!isPublic" class="tool-item" @click="onToggleHistoryVisible">
        <IconFont type="icon-lishi" class="tool-icon" />
        <div>历史对话</div>
      </div>
      <!-- v-if="agentList?.length > 1" -->
      <div class="tool-item" @click="onOpenAgents">
        <IconFont type="icon-zhinengzhuli" class="tool-icon" />
        <div>智能会话</div>
      </div>
    </div>
    <Flex>
      <Sender
        ref="senderRef"
        :value="value"
        :header="isUseUploadFile ? senderHeader : undefined"
        :prefix="isUseUploadFile ? badgeNode : undefined"
        :placeholder="placeholder"
        :loading="loading"
        :onSubmit="handleSubmit"
        :onCancel="handleCancel"
        @update:value="handleUpdateValue"
      />
      <!-- :disabled="disabled" -->
    </Flex>
  </div>
</template>

<script lang="ts" setup>
import { ref, h as vueRender, computed, toRefs, watch } from "vue";
import { Attachments, Sender } from "ant-design-x-vue";
import { CloudUploadOutlined, LinkOutlined } from "@ant-design/icons-vue";
import { Badge, Button, Flex, message as Message } from "ant-design-vue";
import IconFont from "@/components/IconFont/index.vue";
import { API_PREFIX, adaptationUrl } from "@/config/config";
import { getToken } from "../../utils/utils";
import { UploadFile } from "../types/app";
import { getSupportFileType, FILE_EXTS } from "@/DifyChat/utils/file-uploader";
import { SupportUploadFileTypes } from "@/DifyChat/types/type";

const props = withDefaults(
  defineProps<{
    value: string;
    disabled?: boolean;
    loading?: boolean;
    placeholder?: string;
    isUseUploadFile?: boolean;
    isDebugMode?: boolean;
    isPublic?: boolean;
    isFileUploadNumberLimits?: number;
    systemParameters?: any;
    isFileUploadAllowedFileTypes?: string[];
  }>(),
  {
    placeholder: "请输入聊天内容...",
    isPublic: false,
    isFileUploadNumberLimits: 5,
  }
);

const uploadUrl = `${API_PREFIX}${adaptationUrl("/knowledge/kbAssistantConfig/upload")}`;
const authToken = getToken();

const {
  loading,
  placeholder,
  isUseUploadFile,
  isDebugMode,
  isFileUploadNumberLimits,
  systemParameters,
  isFileUploadAllowedFileTypes,
} = toRefs(props);

const emit = defineEmits<{
  (e: "update:value", value: string): void;
  (e: "onSubmit"): void;
  (e: "onCancel"): void;
  (e: "toggleHistoryVisible"): void;
  (e: "openAgents"): void;
  (e: "addConversation"): void;
  (e: "updateUploadFile", file: any): void;
  (e: "onAttachmentsOpenChange", openState: boolean): void;
}>();

const senderRef = ref<InstanceType<typeof Sender> | null>(null);

const open = ref(false);
const items = ref<any[]>([]);

const accepts = computed(() => {
  const allowed_file_types = isFileUploadAllowedFileTypes.value || [];

  return allowed_file_types
    .map((type: any) => {
      return FILE_EXTS[type].map(ext => `.${ext}`).join(",");
    })
    .join(",");
});

const senderHeader = computed(() =>
  vueRender(
    Sender.Header,
    {
      title:
        "附件" +
        `${isFileUploadNumberLimits.value ? "(最多上传" + isFileUploadNumberLimits.value + "个附件)" : ""}`,
      styles: {
        content: {
          padding: 0,
        },
      },
      open: open.value,
      onOpenChange: (v: boolean) => (open.value = v),
      forceRender: true,
    },
    {
      default: () =>
        vueRender(Attachments, {
          beforeUpload: beforeUpload,
          accept: accepts.value,
          action: uploadUrl,
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
          items: items.value,
          maxCount: isFileUploadNumberLimits.value,
          onChange: ({ fileList }: { fileList: UploadFile[] }) => {
            const fileListRef: any[] = [];

            // 过滤掉错误文件
            fileList.forEach((f: any) => {
              if (f.status !== "error") {
                fileListRef.push(f);
              }
            });

            items.value = fileListRef;
            emit("updateUploadFile", fileListRef);
          },
          placeholder: (type: string) =>
            type === "drop"
              ? { title: "拖拽图片到此区域上传" }
              : {
                  icon: vueRender(CloudUploadOutlined),
                  title: "上传文件",
                  description: "点击上传",
                },
          getDropContainer: () => senderRef.value?.nativeElement,
          // accept: ".png, .jpg, .jpeg",
        }),
    }
  )
);

const badgeNode = computed(() =>
  vueRender(
    Badge,
    { dot: items.value.length > 0 && !open.value },
    {
      default: () =>
        vueRender(Button, {
          onClick: () => (open.value = !open.value),
          icon: vueRender(LinkOutlined),
        }),
    }
  )
);

const beforeUpload = (file: any) => {
  const fileName = file.name;
  const fileType = file.type || "";
  const fileSize = file.size || 0;

  // 检查文件类型是否支持
  const supportType = getSupportFileType(fileName, fileType);

  if (!supportType) {
    Message.error("不支持的文件类型");
    file.status = "error"; // 手动设置文件状态为错误
    return false;
  }

  // 根据文件类型判断文件大小限制
  const maxSize =
    supportType === SupportUploadFileTypes.image
      ? systemParameters.value?.image_file_size_limit
      : systemParameters.value?.file_size_limit;

  // 转换为MB
  const fileSizeInMB = fileSize / (1024 * 1024);

  if (fileSizeInMB > maxSize) {
    Message.error(`文件大小不能超过 ${maxSize}MB`);
    file.status = "error"; // 手动设置文件状态为错误
    return false;
  }

  return true;
};

const handleSubmit = () => {
  // console.log("handleSubmit");
  emit("onSubmit");
};

const handleCancel = () => {
  // console.log("handleCancel");
  emit("onCancel");
};

const onAddConversation = () => {
  emit("addConversation");
};

const onToggleHistoryVisible = () => {
  emit("toggleHistoryVisible");
};

const onOpenAgents = () => {
  emit("openAgents");
};

const handleUpdateValue = (value: string) => {
  emit("update:value", value);
};

const clearUploadFile = () => {
  items.value = [];
};

const setAttachmentsOpenState = (openState: boolean) => {
  open.value = openState;
};

watch(open, newVal => {
  emit("onAttachmentsOpenChange", newVal);
});

defineExpose({
  clearUploadFile,
  setAttachmentsOpenState,
});
</script>

<style scoped lang="less">
.chat-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  margin: 6px 14px 20px;
  // background: #fff;
}

.tools {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  column-gap: 8px;

  .tool-item {
    display: flex;
    align-items: center;
    padding: 2px 6px;
    color: var(--text-color-secondary);
    font-size: 12px;
    column-gap: 6px;
    background-color: #f6f6f6;
    border-radius: 6px;
    cursor: pointer;

    &:hover {
      background-color: #f1f1f1;
    }
  }
}

.chat-footer-sender {
  background-color: #fff;
}

:deep(.ant-sender) {
  background-color: #fff;
}
</style>

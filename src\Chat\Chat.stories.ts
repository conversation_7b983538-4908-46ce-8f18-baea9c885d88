import { <PERSON>a, <PERSON>Obj } from "@storybook/vue3";
import Chat from "./index.vue";
import { AgentType } from "./type";

const meta: Meta<typeof Chat> = {
  title: "Chat/Chat",
  component: Chat,
  tags: ["autodocs"],
  argTypes: {
    token: { control: "text" },
    agentIds: { control: "array" },
    initialAgentId: { control: "number" },
    chatVisible: { control: "boolean" },
    noInput: { control: "boolean" },
    isDeveloper: { control: "boolean" },
    integrateSystem: { control: "text" },
    isCopilot: { control: "boolean" },
    currentAgentChange: { action: "currentAgentChange" },
    reportMsgEvent: { action: "reportMsgEvent" },
  },
};

export default meta;
type Story = StoryObj<typeof Chat>;

// 基础示例
export const Default: Story = {
  args: {
    chatVisible: true,
    isDeveloper: true,
    noInput: false,
    token: "c198a725-967c-495c-8c28-0f3832dd499b",
    kkServerUrl: "http://192.168.3.20:8012/onlinePreview",
    kkLocalUrl: "http://192.168.3.150:9999",
    isMicro: true,
    // initialAgentId: 1,
  },
  decorators: [
    () => ({
      template: '<div style="height: 800px;"><story /></div>',
      // beforeMount() {
      //   mockApiResponses();
      // }
    }),
  ],
};
// // 开发者模式
// export const DeveloperMode: Story = {
//   args: {
//     ...Default.args,
//     isDeveloper: true,
//   },
//   decorators: Default.decorators,
// };

// // 无输入模式
// export const NoInputMode: Story = {
//   args: {
//     ...Default.args,
//     noInput: true,
//   },
//   decorators: Default.decorators,
// };

// // 指定Agent ID
// export const WithInitialAgent: Story = {
//   args: {
//     ...Default.args,
//     initialAgentId: 1,
//   },
//   decorators: Default.decorators,
// };

// // 移动端视图
// export const MobileView: Story = {
//   args: {
//     ...Default.args,
//   },
//   parameters: {
//     viewport: {
//       defaultViewport: 'mobile1',
//     },
//   },
//   decorators: Default.decorators,
// };

// // Copilot模式
// export const CopilotMode: Story = {
//   args: {
//     ...Default.args,
//     isCopilot: true,
//   },
//   decorators: Default.decorators,
// };

// // 限制Agent列表
// export const LimitedAgents: Story = {
//   args: {
//     ...Default.args,
//     agentIds: [1, 3],
//   },
//   decorators: Default.decorators,
// };

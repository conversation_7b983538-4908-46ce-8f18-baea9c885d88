<template>
  <div :class="['conversation', historyVisible ? 'historyVisible' : '']">
    <div class="rightSection">
      <div class="titleBar" v-show="historyVisible">
        <div class="title">历史对话</div>
        <div class="rightOperation">
          <div class="newConversation" @click="onAddConversation">新对话</div>
          <CloseOutlined class="closeIcon" @click="onCloseConversation" />
        </div>
      </div>
      <div class="searchConversation" v-show="historyVisible">
        <a-input
          placeholder="搜索"
          v-model:value="searchValue"
          class="searchTask"
          allow-clear
        >
          <template #prefix>
            <SearchOutlined class="searchIcon" />
          </template>
        </a-input>
      </div>
      <a-dropdown :trigger="['contextmenu']">
        <div class="conversationList" v-show="historyVisible">
          <div
            v-for="item in filteredConversations"
            :key="item.chatId"
            :class="[
              'conversationItem',
              currentConversation?.chatId === item.chatId
                ? 'activeConversationItem'
                : '',
            ]"
            @click="() => onSelectConversation(item)"
            @contextmenu.prevent="() => showContextMenu(item)"
          >
            <div class="conversationContent">
              <div class="topTitleBar">
                <div class="conversationTitleBar">
                  <div class="conversationName">{{ item.chatName }}</div>
                  <div
                    v-if="currentConversation?.chatId === item.chatId"
                    class="currentConversation"
                  >
                    当前对话
                  </div>
                </div>
                <div class="conversationTime">
                  {{ convertTime(item.lastTime || "") }}
                </div>
              </div>
              <div class="bottomSection">
                <div class="subTitle">{{ item.lastQuestion }}</div>
                <DeleteOutlined
                  class="deleteIcon"
                  @click.stop="() => onDeleteConversation(item.chatId)"
                />
              </div>
            </div>
          </div>
        </div>
        <template #overlay>
          <a-menu
            @click="
              info => onOperate(String(info.key), contextMenuConversation!)
            "
          >
            <a-menu-item key="editName">修改对话名称</a-menu-item>
            <a-menu-item key="delete">删除</a-menu-item>
          </a-menu>
        </template>
        <div ref="contextMenuRef" style="display: none"></div>
      </a-dropdown>
    </div>
    <ConversationModal
      :visible="editModalVisible"
      :edit-conversation="editConversation"
      :on-close="() => setEditModalVisible(false)"
      :on-finish="
        () => {
          setEditModalVisible(false);
          updateData();
        }
      "
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from "vue";
import { Dropdown, Input, Menu } from "ant-design-vue";
import {
  CloseOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import ConversationModal from "../components/ConversationModal/index.vue";
import {
  deleteConversation,
  getAllConversations,
  saveConversation,
} from "../service";
import { ConversationDetailType, AgentType } from "../type";
import type { ClientAgentType } from "@/types/client";
import { DEFAULT_CONVERSATION_NAME } from "../constants";
import dayjs from "dayjs";
// 导入用于中文本地化的语言包
import "dayjs/locale/zh-cn";

// 设置dayjs语言为中文
dayjs.locale("zh-cn");

export default defineComponent({
  name: "Conversation",
  components: {
    ConversationModal,
    AInput: Input,
    ADropdown: Dropdown,
    AMenu: Menu,
    AMenuItem: Menu.Item,
    CloseOutlined,
    DeleteOutlined,
    SearchOutlined,
  },
  props: {
    currentClientAgent: {
      type: Object as () => ClientAgentType,
      required: true,
    },
    // currentAgent: {
    //   type: Object as () => ClientAgentType,
    //   default: undefined,
    // },
    currentConversation: {
      type: Object as () => ConversationDetailType,
      default: undefined,
    },
    historyVisible: {
      type: Boolean,
      default: false,
    },
    onSelectConversation: {
      type: Function as unknown as () => (
        conversation: ConversationDetailType,
        sendMsgParams?: any,
        isAdd?: boolean
      ) => void,
      required: true,
    },
    onCloseConversation: {
      type: Function as unknown as () => () => void,
      required: true,
    },
  },
  setup(props, { expose }) {
    const conversations = ref<ConversationDetailType[]>([]);
    const editModalVisible = ref(false);
    const editConversation = ref<ConversationDetailType>();
    const searchValue = ref("");
    const contextMenuVisible = ref(true);
    const contextMenuConversation = ref<ConversationDetailType>();
    const contextMenuRef = ref<HTMLElement | null>(null);

    const currentAgent = computed<AgentType>(() => {
      const chatbiConfig = props.currentClientAgent?.chatbiConfig;
      if (chatbiConfig) {
        return JSON.parse(chatbiConfig as unknown as string);
      } else {
        return {};
      }
    });

    const filteredConversations = computed(() => {
      return conversations.value.filter(
        conversation =>
          searchValue.value === "" ||
          conversation.chatName
            .toLowerCase()
            .includes(searchValue.value.toLowerCase())
      );
    });

    const updateData = async (agentId?: number) => {
      const { data } = await getAllConversations(
        agentId || currentAgent.value.id
      );
      const conversationList = data || [];

      conversations.value = conversationList.slice(0, 200);
      return conversationList;
    };

    const onDeleteConversation = async (id: number) => {
      await deleteConversation(id);
      initData();
    };

    const addConversation = async (sendMsgParams?: any) => {
      const agentId = sendMsgParams?.agentId || currentAgent.value.id;
      await saveConversation(DEFAULT_CONVERSATION_NAME, agentId);
      return updateData(agentId);
    };

    const onAddConversation = async (sendMsgParams?: any) => {
      const data = await addConversation(sendMsgParams);
      if (data.length > 0) {
        props.onSelectConversation(data[0], sendMsgParams, true);
      }
    };

    const initData = async () => {
      const data = await updateData();

      console.log("data", data);
      if (data.length > 0) {
        props.onSelectConversation(data[0]);
      } else {
        onAddConversation();
      }
    };

    const convertTime = (date: string) => {
      const now = dayjs();
      const inputDate = dayjs(date);
      const diffMinutes = now.diff(inputDate, "minutes");

      if (diffMinutes < 1) {
        return "刚刚";
      } else if (inputDate.isSame(now, "day")) {
        return inputDate.format("HH:mm");
      } else if (inputDate.isSame(now.subtract(1, "day"), "day")) {
        return "昨天";
      }
      return inputDate.format("MM/DD");
    };

    const onOperate = (key: string, conversation: ConversationDetailType) => {
      if (key === "editName") {
        editConversation.value = conversation;
        editModalVisible.value = true;
      } else if (key === "delete") {
        onDeleteConversation(conversation.chatId);
      }
    };

    const setEditModalVisible = (visible: boolean) => {
      editModalVisible.value = visible;
    };

    const showContextMenu = (conversation: ConversationDetailType) => {
      console.log("showContextMenu 右键", conversation);
      contextMenuConversation.value = conversation;
      contextMenuVisible.value = true;
    };

    watch(
      () => currentAgent,
      newAgent => {
        if (newAgent) {
          if (newAgent.initialSendMsgParams) {
            onAddConversation(newAgent.initialSendMsgParams);
          } else {
            initData();
          }
        }
      },
      { immediate: true }
    );

    // 暴露方法给父组件
    expose({
      updateData,
      onAddConversation,
    });

    return {
      conversations,
      filteredConversations,
      editModalVisible,
      editConversation,
      searchValue,
      contextMenuVisible,
      contextMenuConversation,
      contextMenuRef,
      updateData,
      onDeleteConversation,
      onAddConversation,
      onOperate,
      convertTime,
      setEditModalVisible,
      showContextMenu,
    };
  },
});
</script>

<style lang="less" scoped>
.conversation {
  position: relative;
  width: 0;
  height: 100%;
  background: #fff;

  .rightSection {
    width: 100%;
    height: 100%;

    .titleBar {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        color: var(--text-color);
        font-weight: 500;
        font-size: 15px;
      }

      .rightOperation {
        display: flex;
        align-items: center;
        column-gap: 12px;

        .newConversation {
          color: var(--text-color);
          font-size: 14px;
          cursor: pointer;

          &:hover {
            color: var(--chat-blue);
          }
        }

        .closeIcon {
          color: var(--text-color);
          font-size: 16px;
          cursor: pointer;

          &:hover {
            color: var(--chat-blue);
          }
        }
      }
    }

    .searchConversation {
      display: flex;
      align-items: center;
      padding: 12px 0 10px;

      .searchIcon {
        color: #999 !important;
      }

      .searchTask {
        font-size: 13px;
        background-color: #f5f5f5;
        border: 0;
        border-radius: 4px;
        box-shadow: none !important;

        /* Vue3 不支持:global，使用深度选择器代替 */
        :deep(.ant-input) {
          font-size: 13px !important;
          background-color: transparent !important;
        }
      }
    }

    .conversationList {
      display: flex;
      flex-direction: column;
      height: calc(100% - 70px);
      padding: 2px 0 0;
      overflow-y: auto;
      row-gap: 12px;

      .conversationItem {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border: 1px solid #efefef;
        border-radius: 8px;
        cursor: pointer;

        .conversationContent {
          width: 100%;

          .topTitleBar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .conversationTitleBar {
              display: flex;
              align-items: center;
              column-gap: 6px;

              .conversationName {
                max-width: 300px;
                margin-right: 2px;
                overflow: hidden;
                color: var(--text-color);
                font-weight: 500;
                font-size: 14px;
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              .currentConversation {
                padding: 0 4px;
                color: var(--chat-blue);
                font-size: 12px;
                background-color: var(--light-blue-background);
                border-radius: 4px;
              }
            }

            .conversationTime {
              color: var(--text-color-six);
              font-size: 12px;
            }
          }

          .bottomSection {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 4px;

            .subTitle {
              width: 350px;
              overflow: hidden;
              color: var(--text-color-six);
              font-size: 12px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .deleteIcon {
              color: var(--text-color-six);
              font-size: 14px;
              cursor: pointer;

              &:hover {
                color: var(--chat-blue);
              }
            }
          }
        }

        &:hover,
        &.activeConversationItem {
          background-color: #f0f0f0;
        }
      }
    }
  }

  &.historyVisible {
    width: 400px;
    padding: 10px 16px;
    border-left: 1px solid #f1f1f1;
    z-index: 99;
  }
}
</style>
